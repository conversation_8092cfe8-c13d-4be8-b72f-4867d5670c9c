# Text-to-Speech (TTS) Usage Guide

## Overview
The selfbot now supports three high-quality TTS providers:
- **Gemini** (Premium quality, 30 voices, 24 languages)
- **Groq** (High quality, 23 voices, English/Arabic)
- **Cloudflare** (Fast, efficient, multilingual)

## Basic Usage

### Simple TTS (Default: Cloudflare)
```
.tts Hello, this is a test message!
```

### Using Gemini TTS (Recommended for best quality)
```
.tts -gemini Hello, this is premium quality speech!
```

### Using Groq TTS
```
.tts -groq Hello, this is high quality speech!
```

## Voice Selection

### Gemini Voices (30 available)
```
.tts -gemini voice=Puck I sound upbeat and energetic!
.tts -gemini voice=<PERSON>re I have a firm and confident voice.
.tts -gemini voice=Charon I provide informative narration.
.tts -gemini voice=Enceladus I speak with a breathy tone.
.tts -gemini voice=Sulafat I have a warm and friendly voice.
```

**Popular Gemini Voices:**
- `<PERSON><PERSON>` - Firm and confident
- `Puck` - Upbeat and energetic  
- `<PERSON>ron` - Informative and clear
- `Zephyr` - Bright and cheerful
- `Fenrir` - Excitable and dynamic
- `Enceladus` - Breathy and soft
- `Achernar` - Soft and gentle
- `Sulafat` - Warm and friendly

### Groq Voices
```
.tts -groq voice=Fritz-PlayAI This is Fritz speaking!
.tts -groq voice=Celeste-PlayAI I'm Celeste with a lovely voice.
.tts -groq voice=Thunder-PlayAI I have a powerful voice!
```

## Language Support

### Multilingual Examples
```
.tts -gemini fr Bonjour, comment allez-vous?
.tts -gemini es Hola, ¿cómo estás?
.tts -gemini de Hallo, wie geht es dir?
.tts -groq ar مرحبا بالعالم
```

**Supported Languages:**
English, French, Spanish, German, Italian, Portuguese, Polish, Turkish, Russian, Dutch, Czech, Arabic, Chinese, Japanese, Korean, and more.

## Reply to Messages

You can reply to any text message and convert it to speech:

1. Reply to a message with: `.tts -gemini voice=Puck`
2. The bot will convert the replied message to speech using the specified voice

## Advanced Examples

### Professional Narration
```
.tts -gemini voice=Charon Welcome to our presentation on artificial intelligence.
```

### Casual Conversation
```
.tts -gemini voice=Puck Hey everyone! Hope you're having a great day!
```

### Soft Announcement
```
.tts -gemini voice=Achernar Please remember to turn off your devices.
```

### Energetic Message
```
.tts -gemini voice=Fenrir Let's get this party started!
```

## Tips for Best Results

1. **Use Gemini for premium quality** - It offers the best voice quality and most natural speech
2. **Choose appropriate voices** - Match the voice personality to your message tone
3. **Keep messages concise** - Shorter messages work better for voice messages
4. **Try different voices** - Experiment to find your favorites
5. **Use language codes** - Specify language for non-English text

## Provider Comparison

| Provider   | Quality | Voices | Languages | Speed | Best For |
|------------|---------|--------|-----------|-------|----------|
| Gemini     | Premium | 30     | 24        | Fast  | All purposes |
| Groq       | High    | 23     | 2         | Fast  | English/Arabic |
| Cloudflare | Good    | 1      | 15+       | Fastest | Quick messages |

## Troubleshooting

If TTS doesn't work:
1. Check your API keys are configured
2. Ensure you have internet connection
3. Try a different provider
4. Check the help: `.help tts`

## Getting Help

- `.help tts` - Detailed TTS help
- `.help` - General bot help

Enjoy high-quality text-to-speech with your selfbot! 🎵
