from pyrogram import Client, filters
from helpers import gemini_cli, cloudflare_ai
from google.genai import types
from typing import Optional, <PERSON>ple
import os
from PIL import Image
from io import BytesIO
import logging

# Define function schema for Gemini
PROFILE_FUNCTIONS = [
    types.FunctionDeclaration(
        name="update_telegram_profile",
        description="Update Telegram profile settings like username, first name, or profile photo",
        parameters={
            "type": "object",
            "properties": {
                "username": {
                    "type": "string",
                    "description": "New username (5-32 characters, alphanumeric and underscores)"
                },
                "first_name": {
                    "type": "string",
                    "description": "New first name (1-64 characters)"
                },
                "generate_photo": {
                    "type": "boolean",
                    "description": "Whether to generate and set a new profile photo"
                },
                "photo_prompt": {
                    "type": "string",
                    "description": "Description for generating the profile photo"
                }
            }
        }
    )
]

async def validate_username(username: str) -> tuple[bool, Optional[str]]:
    """Validate Telegram username requirements"""
    if not username:
        return False, "Username cannot be empty"
    if not (5 <= len(username) <= 32):
        return False, "Username must be between 5 and 32 characters"
    if not username.replace('_', '').isalnum():
        return False, "Username can only contain alphanumeric characters and underscores"
    if not any(c.isalnum() for c in username):
        return False, "Username must contain at least one letter or number"
    return True, None

async def generate_profile_photo(prompt: str, provider: str = "auto") -> Tuple[Optional[str], str]:
    """Generate profile photo using specified provider or auto-fallback

    Args:
        prompt: Description for the profile photo
        provider: 'gemini', 'cloudflare', or 'auto' (try both)

    Returns:
        Tuple of (image_path, provider_used) or (None, error_message)
    """
    # Try Gemini first if specified or in auto mode
    if provider in ["gemini", "auto"] and googe_cli is not None:
        try:
            logging.info("Attempting to generate profile photo with Gemini")
            response = googe_cli.models.generate_content(
                model="gemini-2.0-flash-exp-image-generation",
                contents=f"Generate a professional profile photo: {prompt}",
                config=types.GenerateContentConfig(
                    response_modalities=['Text', 'Image'],
                )
            )

            for part in response.candidates[0].content.parts:
                if part.inline_data is not None:
                    image = Image.open(BytesIO(part.inline_data.data))
                    # Ensure square aspect ratio
                    size = min(image.size)
                    image = image.crop((0, 0, size, size))
                    image_path = "profile_photo.jpg"
                    image.save(image_path, "JPEG", quality=95)
                    return image_path, "gemini"

            # If we get here, Gemini didn't return an image
            if provider == "gemini":
                return None, "Gemini did not return an image"
        except Exception as e:
            error_msg = f"Error with Gemini: {str(e)}"
            logging.error(error_msg)
            if provider == "gemini":
                return None, error_msg

    # Try Cloudflare if specified or if Gemini failed in auto mode
    if provider in ["cloudflare", "auto"] and cloudflare_ai is not None:
        try:
            logging.info("Attempting to generate profile photo with Cloudflare")
            # Use Cloudflare's Stable Diffusion model
            response = cloudflare_ai.run_model(
                model="@cf/stability-ai/stable-diffusion-xl-base-1.0",
                messages={
                    "prompt": f"Professional profile photo, portrait style, high quality, {prompt}",
                    "num_steps": 20  # Maximum allowed steps for quality
                }
            )

            # Cloudflare returns the image directly
            if response:
                # Save the image
                image = Image.open(BytesIO(response))
                # Ensure square aspect ratio
                width, height = image.size
                size = min(width, height)
                left = (width - size) // 2
                top = (height - size) // 2
                image = image.crop((left, top, left + size, top + size))
                image_path = "profile_photo.jpg"
                image.save(image_path, "JPEG", quality=95)
                return image_path, "cloudflare"
            else:
                return None, "Cloudflare did not return an image"
        except Exception as e:
            error_msg = f"Error with Cloudflare: {str(e)}"
            logging.error(error_msg)
            return None, error_msg

    # If we get here, all attempts failed or no providers were available
    if googe_cli is None and cloudflare_ai is None:
        return None, "No image generation providers available"
    elif provider == "gemini" and googe_cli is None:
        return None, "Gemini client is not available"
    elif provider == "cloudflare" and cloudflare_ai is None:
        return None, "Cloudflare client is not available"
    else:
        return None, "Failed to generate image with all available providers"

async def try_alternative_username(client, base_username: str) -> tuple[bool, Optional[str], Optional[str]]:
    """Try variations of username until finding an available one."""
    attempts = [
        base_username,
        f"{base_username}_ai",
        f"{base_username}_{str(hash(str(client.me.id)))[:4]}",  # Add unique suffix based on user ID
        f"the_{base_username}",
        f"{base_username}_pro"
    ]

    for username in attempts:
        try:
            await client.set_username(username)
            return True, username, None
        except Exception as e:
            if "USERNAME_OCCUPIED" not in str(e):
                return False, None, str(e)
    return False, None, "All username variations were taken"

@Client.on_message(filters.me & filters.command(["setprofile", "sp"], prefixes=[".", "!"]))
async def set_profile(client, message):
    try:
        cmd = message.command
        if len(cmd) < 2:
            await message.edit(
                "Usage:\n"
                "`.setprofile generate a cool username for me`\n"
                "`.setprofile suggest a creative first name`\n"
                "`.setprofile create a professional profile with photo`\n"
                "`.setprofile make me look like a tech expert`\n\n"
                "Options:\n"
                "`.setprofile -gemini create a professional profile with photo` - Use Gemini for image generation\n"
                "`.setprofile -cloudflare create a professional profile with photo` - Use Cloudflare for image generation"
            )
            return

        # Check for provider flag
        provider = "auto"  # Default to auto (try both providers)
        if cmd[1].lower() == "-gemini":
            provider = "gemini"
            cmd.pop(1)  # Remove the provider flag
        elif cmd[1].lower() == "-cloudflare" or cmd[1].lower() == "-cf":
            provider = "cloudflare"
            cmd.pop(1)  # Remove the provider flag

        if len(cmd) < 2:
            await message.edit("❌ Please provide a description of what you want after the provider flag.")
            return

        prompt = " ".join(cmd[1:])
        await message.edit("🤔 Thinking about profile updates...")

        # Configure Gemini with function calling
        config = types.GenerateContentConfig(
            temperature=0.7,
            tools=[types.Tool(
                function_declarations=PROFILE_FUNCTIONS
            )]
        )

        # Get response from Gemini
        response = googe_cli.models.generate_content(
            model="gemini-1.5-pro",
            contents=[
                f"Help me update my Telegram profile. Here's what I want: {prompt}\n"
                "Suggest appropriate changes and call the function to update them. "
                "If the request implies a profile photo change, set generate_photo=true "
                "and provide a detailed photo_prompt."
            ],
            config=config
        )

        # Check for function calls in response
        if not response.candidates[0].content.parts[0].function_call:
            await message.edit("❌ Failed to generate profile suggestions")
            return

        # Extract function call details
        function_call = response.candidates[0].content.parts[0].function_call
        params = function_call.args

        # Validate and apply changes
        changes_made = []

        # Handle profile photo generation if requested
        if params.get('generate_photo') and params.get('photo_prompt'):
            await message.edit("🎨 Generating profile photo...")
            photo_path, result_info = await generate_profile_photo(params['photo_prompt'], provider)
            if photo_path:
                try:
                    await client.set_profile_photo(photo=photo_path)
                    changes_made.append(f"✅ Profile photo updated (using {result_info})")
                    os.remove(photo_path)
                except Exception as e:
                    changes_made.append(f"❌ Failed to set profile photo: {str(e)}")
            else:
                changes_made.append(f"❌ Failed to generate profile photo: {result_info}")

        # Handle username update
        if 'username' in params:
            username = params['username'].strip().lower()
            is_valid, error = await validate_username(username)

            if is_valid:
                success, final_username, error = await try_alternative_username(client, username)
                if success:
                    changes_made.append(f"✅ Username changed to @{final_username}")
                else:
                    changes_made.append(f"❌ Failed to set username: {error}")
            else:
                changes_made.append(f"❌ Invalid username: {error}")

        # Handle first name update
        if 'first_name' in params:
            first_name = params['first_name'].strip()
            if 1 <= len(first_name) <= 64:
                try:
                    await client.update_profile(first_name=first_name)
                    changes_made.append(f"✅ First name changed to '{first_name}'")
                except Exception as e:
                    changes_made.append(f"❌ Failed to set first name: {str(e)}")
            else:
                changes_made.append("❌ First name must be between 1 and 64 characters")

        # Show results
        if changes_made:
            await message.edit("\n".join(changes_made))
        else:
            await message.edit("❌ No valid profile changes were suggested")

    except Exception as e:
        await message.edit(f"⚠️ Error: {str(e)}")
