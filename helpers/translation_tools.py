# plugins/translation_tools.py
from pyrogram import Client, filters
from langdetect import detect
from googletrans import Translator
import asyncio
from google import genai
from . import db, gemini_cli, OpenRouterClient, cloudflare_ai
from google.genai import types

# Initialize translator
translator = Translator()

# Define system prompt at module level
SYSTEM_PROMPT = "Act as an expert translator. Translate the following text to the target language, preserving the original meaning, tone, and cultural nuances. Adapt idiomatic expressions as needed to create a natural, contextually accurate translation."

async def ensure_language(text, user_id, db, default_lang='fa', translate_model=None):
    """
    Check if text is in the target language; if not, translate it.
    Uses user's preferred model and language from DB, with langdetect for detection.
    """
    try:
        # Fetch user preferences from DB
        if not translate_model:
            user_prefs = db.translation_prefs.find_one({'user_id': user_id}) or {}
            target_lang = user_prefs.get('target_lang', default_lang)
            translate_model = user_prefs.get('translate_model', 'googletrans')
        else:
            target_lang = default_lang
        
        # Detect language with langdetect
        detected_lang = detect(text)
        if detected_lang == target_lang:
            return text
        
        if translate_model == 'cloudflare':
            if cloudflare_ai is None:
                raise ValueError("Cloudflare AI client is not properly initialized")
            
            response = cloudflare_ai.run_model(
                model='@cf/meta/m2m100-1.2b',
                messages={
                    "text": text,
                    "source_lang": detected_lang,
                    "target_lang": target_lang
                }
            )
            
            if not response or 'translated_text' not in response:
                raise ValueError("Translation failed: no translated text in response")
                
            return response['translated_text']
            
        elif translate_model == 'googletrans':
            translated = await translator.translate(text, dest=target_lang)
            return translated.text

        elif translate_model == 'gemini':
            cur = db.providers.find_one({'_id': 'google'})
            model = cur['default_model']
            
            # Construct language-specific instruction
            instruction = f"{SYSTEM_PROMPT} Translate to {target_lang}."
            
            response = googe_cli.models.generate_content(
                model=model,
                config=types.GenerateContentConfig(
                    system_instruction=instruction),
                contents=[text.strip()]
            )
            return response.text

        elif translate_model == 'openrouter':
            openrouter_client = OpenRouterClient()
            cur = db.providers.find_one({'_id': 'openrouter'})
            model = cur['default_model']
            
            # Construct language-specific instruction
            instruction = f"{SYSTEM_PROMPT} Translate to {target_lang}."
            
            response = openrouter_client.chat_completions_create(
                model=model, 
                messages=[
                    {'role': 'system', 'content': instruction},
                    {'role': 'user', 'content': text.strip()}
                ])
            return response['choices'][0]['message']['content']

        else:
            raise ValueError(f"Unsupported translation model: {translate_model}")
        
    except Exception as e:
        print(f"Translation error: {str(e)}")
        return text  # Return original text on failure


@Client.on_message(filters.command('settranslate', prefixes=['.', '!']) & filters.me)
async def set_translation_prefs(client, message):
    try:
        cmd = message.command
        if len(cmd) < 3:
            await message.edit(
                "❌ Usage: `.settranslate <model> <language>`\n"
                "Models: `googletrans`, `gemini`\n"
                "Languages: e.g., `fa` (Persian), `en` (English), `es` (Spanish)\n"
                "Example: `.settranslate gemini fa`"
            )
            return
        
        model = cmd[1].lower()
        language = cmd[2].lower()
        
        valid_models = ['googletrans', 'gemini', 'openrouter']
        if model not in valid_models:
            await message.edit(f"❌ Invalid model. Use one of: {', '.join(valid_models)}")
            return
        
        db.translation_prefs.update_one(
            {'user_id': message.from_user.id},
            {'$set': {'translate_model': model, 'target_lang': language}},
            upsert=True
        )
        
        await message.edit(f"✅ Translation preferences set: Model=`{model}`, Language=`{language}`")
    except Exception as e:
        await message.edit(f"⚠️ Error setting translation preferences: {str(e)}")
