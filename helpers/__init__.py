#helpers/__init__.py
import os, json
import requests
from pymongo import MongoClient
from groq import Groq
from google import genai
from google.genai.types import Tool, GenerateContentConfig, GoogleSearch
from typing import List, Dict, Optional
import logging
import logging.handlers
import pathlib
import httpx

# Set up logging first, before any other imports
def setup_logging():
    # Create logs directory if it doesn't exist
    log_dir = pathlib.Path("logs")
    log_dir.mkdir(exist_ok=True)

    # Configure logging
    logger = logging.getLogger('selfbot')
    logger.setLevel(logging.INFO)

    # File handler for all logs
    file_handler = logging.handlers.RotatingFileHandler(
        'logs/selfbot.log',
        maxBytes=5*1024*1024,  # 5MB
        backupCount=5
    )
    file_handler.setFormatter(
        logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    )
    logger.addHandler(file_handler)

    # Error file handler
    error_handler = logging.handlers.RotatingFileHandler(
        'logs/error.log',
        maxBytes=5*1024*1024,  # 5MB
        backupCount=5
    )
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(
        logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    )
    logger.addHandler(error_handler)

    return logger

# Initialize logger
logger = setup_logging()

# Now import config after logger is set up
from .config import config
from .prompts import PROMPTS

cli = MongoClient()
db = cli.self_bot

# Log MongoDB connection
try:
    db.command('ping')
    logger.info("Successfully connected to MongoDB")
except Exception as e:
    logger.error(f"Failed to connect to MongoDB: {str(e)}")

gemini_safety_settings = [
    genai.types.SafetySetting(category='HARM_CATEGORY_HARASSMENT', threshold='BLOCK_NONE'),
    genai.types.SafetySetting(category='HARM_CATEGORY_HATE_SPEECH', threshold='BLOCK_NONE'),
    genai.types.SafetySetting(category='HARM_CATEGORY_SEXUALLY_EXPLICIT', threshold='BLOCK_NONE'),
    genai.types.SafetySetting(category='HARM_CATEGORY_DANGEROUS_CONTENT', threshold='BLOCK_NONE'),
    genai.types.SafetySetting(category='HARM_CATEGORY_CIVIC_INTEGRITY', threshold='BLOCK_NONE'),
]


# Configure Google AI with web search capability
google_search_tool = Tool(
    google_search=GoogleSearch()
)

# Initialize API clients with logging
try:
    gemini_api_key = config.get_google_api_key()
    if gemini_api_key:
        gemini_cli = genai.Client(api_key=gemini_api_key)
        logger.info("Successfully initialized Gemini client for TTS.")
    else:
        logger.warning("Google/Gemini API key not found. Gemini TTS will be unavailable.")
        gemini_cli = None
except Exception as e:
    logger.error(f"Failed to initialize Gemini client for TTS: {str(e)}")
    gemini_cli = None

try:
    groq_api_key = config.get_groq_api_key()
    if groq_api_key:
        groq_cli = Groq(api_key=groq_api_key)
        logger.info("Successfully initialized Groq client")
    else:
        logger.warning("Groq API key not found. Groq client will be unavailable.")
        groq_cli = None
except Exception as e:
    logger.error(f"Failed to initialize Groq client: {str(e)}")
    groq_cli = None

# Import model-related classes and data from models.py
from .models import MiniToolAIClient, OpenRouterClient, providers, get_gemini_models, get_groq_models
from .cloudflare import CloudflareAIClient

# Initialize MiniToolAI client
minitoolai_client = MiniToolAIClient(api_key=config.get_minitool_api_key())

# Update providers dictionary with dynamic models if clients are available

# Update Gemini models
if gemini_cli is not None:
    try:
        gemini_models = get_gemini_models(gemini_cli)
        # Update the 'google' provider with the fetched models
        if gemini_models and len(gemini_models) > 0:
            # Update existing provider with fetched models
            providers['google']['models'] = gemini_models
            # Set a sensible default model
            if 'gemini-1.5-pro' in gemini_models:
                providers['google']['default_model'] = 'gemini-1.5-pro'
            elif len(gemini_models) > 0:
                providers['google']['default_model'] = gemini_models[0]

            logger.info(f"Successfully fetched {len(gemini_models)} Gemini models")
    except Exception as e:
        logger.error(f"Failed to update Gemini models: {str(e)}")

# Update Groq models
if groq_cli is not None:
    try:
        groq_models = get_groq_models(groq_cli)
        # Update the 'groq' provider with the fetched models
        if groq_models and len(groq_models) > 0:
            # Update existing provider with fetched models
            providers['groq']['models'] = groq_models
            # Set a sensible default model
            if 'llama-3.3-70b-versatile' in groq_models:
                providers['groq']['default_model'] = 'llama-3.3-70b-versatile'
            elif len(groq_models) > 0:
                providers['groq']['default_model'] = groq_models[0]

            logger.info(f"Successfully fetched {len(groq_models)} Groq models")
    except Exception as e:
        logger.error(f"Failed to update Groq models: {str(e)}")


# OpenRouter client initialization
try:
    openrouter_api_key = config.get_openrouter_api_key()
    if openrouter_api_key:
        openrouter_client = OpenRouterClient(api_key=openrouter_api_key)
        logger.info("Successfully initialized OpenRouter client")
    else:
        openrouter_client = None
        logger.warning("OpenRouter API key not found")
except Exception as e:
    logger.error(f"Failed to initialize OpenRouter client: {str(e)}")
    openrouter_client = None

# Initialize Cloudflare AI client with proper error handling
try:
    cf_creds = config.get_cloudflare_credentials()
    account_id = cf_creds['account_id']
    api_token = cf_creds['api_key']

    if not account_id or not api_token:
        logger.error("Missing Cloudflare credentials")
        cloudflare_ai = None
    else:
        cloudflare_ai = CloudflareAIClient(account_id=account_id, api_token=api_token)
        logger.info("Successfully initialized Cloudflare AI client")
except Exception as e:
    logger.error(f"Failed to initialize Cloudflare AI client: {str(e)}")
    cloudflare_ai = None