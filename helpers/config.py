import os
from typing import Any, Dict, Optional
from pathlib import Path
import logging

# Get the logger instance
logger = logging.getLogger('selfbot')

class ConfigManager:
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            logger.info("Creating new ConfigManager instance")
        return cls._instance

    def __init__(self):
        if not hasattr(self, 'initialized'):
            self._load_env_file()
            logger.info("ConfigManager initialized with .env file")
            self.initialized = True

    def _load_env_file(self) -> None:
        """Load environment variables from .env file if it exists."""
        env_file = Path('.env')
        if env_file.exists():
            logger.info("Loading configuration from .env file")
            with open(env_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        key = key.strip()
                        value = value.strip().strip('"').strip("'")
                        if key and not os.getenv(key):  # Don't override existing env vars
                            os.environ[key] = value
            logger.info("Environment variables loaded from .env file")
        else:
            logger.info("No .env file found, using system environment variables only")

    def get_env(self, key: str, default: Any = None) -> Any:
        """Get an environment variable with optional default."""
        value = os.getenv(key, default)
        if value is None:
            logger.warning(f"Environment variable {key} not found, using default: {default}")
        else:
            logger.debug(f"Retrieved environment variable {key}")
        return value

    def get_telegram_credentials(self) -> Dict[str, str]:
        """Get Telegram credentials from environment variables."""
        logger.info("Retrieving Telegram credentials")
        creds = {
            'api_id': self.get_env('TELEGRAM_API_ID'),
            'api_hash': self.get_env('TELEGRAM_API_HASH'),
            'session': self.get_env('TELEGRAM_SESSION', 'my_account')
        }

        if not all([creds['api_id'], creds['api_hash']]):
            logger.error("Missing required Telegram credentials (TELEGRAM_API_ID, TELEGRAM_API_HASH)")
        return creds

    def get_proxy_config(self) -> Optional[Dict[str, Any]]:
        """Get proxy configuration from environment variables."""
        logger.info("Retrieving proxy configuration")
        enabled = self.get_env('PROXY_ENABLED', 'false').lower() in ('true', '1', 'yes', 'on')

        if enabled:
            proxy_config = {
                'scheme': self.get_env('PROXY_SCHEME', 'socks5'),
                'hostname': self.get_env('PROXY_HOSTNAME', 'localhost'),
                'port': int(self.get_env('PROXY_PORT', '10808'))
            }
            logger.info(f"Proxy enabled: {proxy_config['scheme']}://{proxy_config['hostname']}:{proxy_config['port']}")
            return proxy_config

        logger.info("Proxy disabled")
        return None

    # Convenience methods for API keys
    def get_google_api_key(self) -> Optional[str]:
        return self.get_env('GOOGLE_API_KEY') or self.get_env('GEMINI_API_KEY')

    def get_groq_api_key(self) -> Optional[str]:
        return self.get_env('GROQ_API_KEY')

    def get_openrouter_api_key(self) -> Optional[str]:
        return self.get_env('OPENROUTER_API_KEY')

    def get_cloudflare_credentials(self) -> Dict[str, Optional[str]]:
        return {
            'account_id': self.get_env('CLOUDFLARE_ACCOUNT_ID'),
            'api_key': self.get_env('CLOUDFLARE_API_KEY')
        }

    def get_minitool_api_key(self) -> Optional[str]:
        return self.get_env('MINITOOL_API_KEY')

config = ConfigManager()
